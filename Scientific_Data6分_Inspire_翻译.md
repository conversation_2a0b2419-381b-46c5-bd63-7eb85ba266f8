# INSPIRE：围手术期医学公开研究数据集

## 摘要

我们提出了INSPIRE数据集，这是一个围手术期医学的公开研究数据集，包含韩国一所学术机构在2011年至2020年十年间约130,000例外科手术。这个综合数据集包括患者特征，如年龄、性别、美国麻醉医师协会身体状态分级、诊断、手术程序代码、科室和麻醉类型。该数据集还包括手术室、普通病房和重症监护病房(ICU)的生命体征，从入院前6个月到出院后6个月的实验室结果，以及住院期间的药物治疗。并发症包括总住院时间、ICU住院时间和院内死亡。我们希望这个数据集能够激发围手术期医学的合作研究和发展，并作为可重现的外部验证数据集来改善手术结果。

## 背景与总结

手术后主要并发症发生在约7-15%的患者中。然而，对罕见并发症（如术后死亡率、呼吸衰竭或心肌损伤）的深入研究需要一个全面的大型数据集以获得足够的统计功效。存在一些大型注册数据库，如国家麻醉临床结果注册数据库或国家外科质量改进计划，这些是改善外科患者结果的全国性项目。然而，这些数据仅对参与机构的研究人员开放，并且不包括详细数据，如实验室或生理测量的时间序列。

VitalDB是一个公开可用的外科患者术中数据集，提供高分辨率多参数数据。然而，在韩国单一中心的6,388例病例中，它仅包括57例(0.9%)院内死亡病例。医学信息学手术室生命体征和事件存储库(MOVER)最近发布，是第一个也是最大的公开可用围手术期数据集，包含来自58,799名患者的83,468例外科病例。MOVER包括多样化的临床结果，如死亡率、ICU转入、心血管或神经系统并发症，以及高保真波形数据。虽然VitalDB专注于术中数据集，MOVER将开放数据集的范围扩展到覆盖整个围手术期。通过使涵盖围手术期的数据集公开可用，MOVER为世界各地不同群体的围手术期结果多学科研究提供了可能性。然而，作为MOVER一部分的SIS只有代表每次手术的单一标识符，不允许追踪多次手术的患者。此外，MOVER需要额外的合并过程来将两个不同结构的数据集(SIS和EPIC)合并为一个独特的数据集。

重症监护医学信息市场(MIMIC)是另一个公开可用的ICU患者数据集，来自美国单一中心，提供了更广泛的并发症患者范围。然而，MIMIC仅限于入住ICU或急诊科的特定患者队列。

近年来，引入了大量机器学习模型，旨在改善围手术期医学中的风险分层、预测不良事件和恶化警报。然而，这些模型的一个普遍问题是缺乏外部验证，这阻碍了它们在临床实践中实施之前的无偏、客观性能评估。开放数据集的创建可以在这种情况下发挥关键作用，通过为研究社区提供客观验证集、通过合作加速技术发展，以及通过减少临床数据可及性差异来推进医学知识。

在此，我们提出了一个名为INSPIRE的合作研究数据集，即创新研究环境的信息性外科患者数据集，它包含用于围手术期医学合作研究和发展的各种数据。通过INSPIRE，研究人员可以研究罕见结果，如术后死亡率或ICU入院，这些由于发生率低而被认为难以研究，调查风险因素，并使用广泛的围手术期数据（如诊断、实验室结果、生命体征和药物）开发与手术结果相关的模型。该数据集的主要目的是促进新型预测模型的开发，并作为现有模型的外部验证资源。通过支持这些研究努力，我们希望"激发"围手术期医学的创新研究并改善外科患者结果。

## 方法

### 伦理批准
本研究获得首尔国立大学医院(SNUH)机构审查委员会(IRB)批准(IRB编号：H-2210-078-1368)。由于研究设计的回顾性性质，IRB也豁免了知情同意。此外，SNUH的机构数据审查委员会(DRB)在确认匿名化的充分性后批准向公众发布数据集(DRB编号：BD-R-2022-11-02)。

### 患者人群
纳入2011年1月至2020年12月期间在SNUH接受全身麻醉、椎管内麻醉、区域麻醉和监护麻醉护理下手术的所有患者。排除手术当天年龄小于18岁或大于90岁的患者，以及拒绝披露其入院信息或向公众披露（如大众媒体文章）的患者。根据DRB的决定，我们随机排除了50%的患者来构成公开发布的数据集（图1）。队列的基线特征见表1。

### 数据获取
从SNUH的临床数据仓库(SUPREME 1.0和2.0，首尔国立大学医院，韩国首尔)中提取手术和麻醉相关变量、诊断、生命体征、实验室结果或药物处方和给药。

手术室内患者的生命体征和麻醉机设置每1分钟自动记录在麻醉记录上。麻醉记录包括尿量、估计失血量、输注液体或血制品体积、药物以及专业监测设备值（如处理过的脑电图或肺动脉导管）的手动记录，以自由文本形式。

在患者ICU住院期间，一系列参数每小时或按主治医师规定的间隔记录。这些参数包括生命体征、尿量、格拉斯哥昏迷量表和从机械呼吸机获得的指标。生命体征和机械呼吸机衍生变量通过与相应设备类型的数字通信获得，允许手动修改。关于额外生命支持设备（如连续肾脏替代治疗(CRRT)、体外膜肺氧合(ECMO)或主动脉内球囊反搏(IABP)）的值每4-8小时以自由文本形式记录并转换为二元变量。

在患者普通病房住院期间，根据医师医嘱每天测量和记录4-6次生命体征。诊断根据国际疾病分类第10版临床修订版(ICD-10-CM)记录。手术名称从手术记录和国民健康保险服务索赔数据中以自由文本形式提取。

### 数据处理
除手术相关变量外，所有变量（如实验室检查结果、生命体征、诊断和药物给药）都提取了每次手术前后90天内测量的值。实验室数据类型包括动脉血气分析、血细胞计数、肾功能和肝功能检查、凝血检查、糖化血红蛋白、乳酸和心肌酶。包括特殊备注（如"重新检测"、"凝固"或"稀释"）的实验室结果被移除。

所有生命体征、实验室结果和专业设备（如机械呼吸机、CRRT、ECMO和IABP）的使用都聚合为中位值，最大分辨率为5分钟。CRRT、ECMO和IABP的使用通过至少一个自动记录的临床观察记录的存在来确定，而机械通气的使用通过相关临床观察记录（如设定PEEP或设定FiO2）或GCS中"E"或"T"的口头输入的存在来确定。

### 匿名化
数据按受试者基础提取。对于每个受试者，研究期间首次入院手术时间被视为时间零点。所有时间都转换为相对于时间零点的时间（分钟）。

根据韩国个人信息保护法（2023年修订版），所有能够单独或与其他信息结合识别个人的个人信息，如姓名、身份证号码（居民登记号、护照号、保险号等）、地址和电话号码，在共享前应被移除。因此，我们从数据提取过程中排除了除机构病历号外的所有个人标识符。然后使用Python 3.10 NumPy 1.23库中的random.choice方法和PCG-64伪随机数生成器，将病历号替换为100,000,000到199,999,999之间的唯一随机数后重命名为subject_id。类似地，每次入院和手术都分配了以'2'和'4'开头的唯一随机9位标识符(hadm_id和op_id)。

从SNUH医院信息办公室获得了选择退出数据共享的患者名单，确定了16,176名患者的25,946例手术病例需要排除。由于我们决定排除可能使用广泛可获得的数据（如公共媒体）识别的患者，我们使用关键词("首尔国立大学医院"或"SNUH")和("手术"或"程序")在Google新闻中搜索。结果搜索到2,332篇文章，我们移除了3名患者的3例病例。

根据韩国个人信息去标识化措施指南，年龄被离散化为五年间隔。例如，50岁包括47.5到52.4之间的年龄范围。此外，所有时间点变量（表示为'_time'）通过将其转换为相对时间进行匿名化，以首次入院时间为时间零点。

通过物理和化学方法从临床状态测量的变量，如生命体征和实验室结果，在分类后使用以降低使用其他信息重新识别的风险。每个变量2.5到97.5百分位数范围内的值被替换为19个段中5%间隔的四舍五入值。例如，2.5到7.4百分位数之间的值被替换为5百分位数值，而7.5到12.4百分位数之间的值被替换为10百分位数值。低于2.5百分位数的值被替换为2.5百分位数值，超过97.5百分位数的值被替换为97.5百分位数值。

最初以ICD-10-CM格式记录的诊断被提取，并提取代码的前三位数字。根据韩国保健福祉部的建议，我们不包括精神和行为障碍、性传播感染、人类免疫缺陷病毒(HIV)相关疾病、终止妊娠和虐待相关诊断、围产期特定疾病、先天性畸形、变形、染色体异常以及韩国罕见疾病管理法定义的罕见疾病的诊断。根据韩国罕见疾病管理法，罕见疾病定义为影响少于20,000人的疾病或难以确定患病率的疾病。韩国保健福祉部通过每年进行的罕见疾病登记统计项目指定和管理韩国罕见疾病清单。截至2022年，清单上有1,165种指定罕见疾病，所有这些诊断都被排除。手术名称通过手动映射转换为ICD-10-PCS的前四个代码，代表部分、身体系统、根手术和身体部位，以降低重新识别的风险。

考虑年龄（5年间隔）和性别作为准标识符，ICD-10-PCS（前4个代码）作为敏感属性，我们计算了k-匿名性为129，l-多样性为58，t-接近性为0.049。即使假设年龄、性别和院内死亡为准标识符，k-匿名性也大于2。其他措施如身高、体重以及诊断和手术代码在韩国不容易大规模获得，因为公共保险是大多数医疗费用的单一支付方。使用ARX数据匿名化工具3.9.1版本，这是一个用于匿名化敏感个人数据的开源软件，我们进行了重新识别风险分析；所有攻击者模型的风险都低于0.002%。鉴于这些结果，我们认为INSPIRE数据集的重新识别风险非常低。

## 数据记录

INSPIRE在PhysioNet上公开可用(https://physionet.org/content/inspire)。INSPIRE数据集由七个表组成（补充表1）。每个表都可以使用subject_id连接。一个subject_id可能匹配一个或多个hadm_id。单个hadm_id可能匹配一个或多个op_id。虽然为了适合研究外科患者进行了一些更改，但大部分结构借鉴了MIMIC数据集。

### 手术表
'operations'表由手术相关变量组成，包括手术时的人口统计学特征、手术或麻醉时间(opstart_time、opend_time、anstart_time或aneend_time)、以ICD-10-PCS前4个字符表示的手术类型(icd10_pcs)、麻醉类型(antype)、体外循环变量、术后ICU入院和出院或院内死亡。

手术时中位年龄为60岁（四分位间距，45-70）。大多数患者的ASA-PS分级为1或2（88%），约10%的手术为急诊手术。关于外科科室，26.5%的所有手术为普通外科，其次是骨科，占所有手术的13.3%。ICU入院和院内死亡分别发生在14,971例（11.4%）和1,581例（1.21%）手术中。与VitalDB的死亡率（0.9%）相比，院内死亡率略高（表1）。

### 诊断表
'diagnosis'表包括医师以ICD-10-CM形式声明的所有诊断，从时间零点前6个月到最后一次手术后出院，除了一组需要移除的预定义敏感诊断（表2）。仅显示ICD-10-CM代码的前三位数字和诊断的相对时间。最常见的诊断是H26，代表与白内障相关的疾病，出现在约9,000名患者中。

### 生命体征表
'vitals'表包括所有术中生命体征、尿量、液体给药、估计失血量、麻醉机设置（如O2吸入流量或麻醉气体浓度）或通气参数（如潮气量或手术期间峰吸气压）。由专业设备测量的变量，如双频指数和区域脑氧饱和度，也包括在内。所有变量都与subject_id和op_id匹配，以无单位的值和5分钟间隔的chart_time显示。参数标签在parameters表中。

虽然大多数生命体征（如心率、呼吸频率或外周血氧饱和度）在大多数手术中存在，但由专业设备测量的变量仅在有限的手术病例中存在。双频指数水平和区域脑氧饱和度分别在65,236例（49.8%）和205例（0.16%）中可用。

### 病房生命体征表
虽然'vitals'表包括术中生命体征，'ward_vitals'表包括在手术室外测量的生命体征。从时间0前6个月到最后一次手术后出院时间，包括所有测量的生命体征。chart_time以5分钟间隔表示，对于测量时间短于5分钟的变量用中位值插补。参数标签在parameters表中。

关于额外的生命支持设备，围手术期ECMO、IABP和CRRT的应用分别在166例（0.17%）、180例（0.18%）和855例（0.86%）患者中发现。

### 实验室表
预定义的实验室变量包含在'labs'表中，带有其值和chart_time。包括从时间零点前6个月到最后一次出院后6个月测量的实验室结果。参数标签在parameters表中。

由于我们的常规术前评估包括在手术前6个月内进行血细胞计数、肾功能和肝功能检查以及凝血检查的实验室测量，相关实验室变量在大多数病例中都能找到。术中实验室测量主要限于有动脉导管患者的床旁动脉血气分析检测，测量间隔最长为2小时。

### 药物表
'medications'表包括从时间0前6个月到最后一次出院时间之间给药的药物数据。表中捕获的信息包括subject_id、作为药物给药时间的chart_time、作为成分名称的drug_name和作为药物给药途径的route。普通病房的液体给药（如平衡晶体液、生理盐水或葡萄糖溶液）不包括在内。为避免使用很少给药的药物重新识别的风险，化疗、免疫治疗、研究药物和给药少于100名患者的药物被排除。

结果，记录了来自99,807名患者的9,926,795次药物给药。其中，确定了1,376种药物和给药途径的独特组合，包括1,238种不同类型的药物。

### 参数表
'parameters'表包括labs、vitals和ward_vitals表中参数的物理单位和人类可读描述。

## 技术验证

在从临床数据仓库初始提取数据后，上述所有过程都由单一研究者(HCL)使用版本控制的Python和可重现构建脚本执行（所有脚本在代码可用性部分描述）。所有代码都由另一位专家(HHL)额外审查。在整个数据集构建过程中，包括原始数据提取、数据整理和处理，另一位专家(LL)评估了每个变量的分布并手动检查结果。INSPIRE数据集中包含和排除的患者在年龄、性别和麻醉类型等基线特征方面没有显著差异（补充表2）。

为了验证数据集的完整性，我们评估了INSPIRE和VitalDB之间实验室项目的匹配率，其中手术包含在两个数据集中。我们基于入院时间匹配手术病例、实验室项目和图表时间。由于所有值在匿名化过程中都被分类，值没有匹配。基于VitalDB的caseid，我们检查了所有实验室测量，如果在INSPIRE中有相同测试的匹配测量，其中结果时间在5分钟窗口内对齐。

匹配率为97.9%。剩余的2.1%不匹配实验室测量（在VitalDB中存在但在INSPIRE中不存在）主要源于VitalDB和INSPIRE之间数据预处理的差异。虽然VitalDB没有移除带有特殊备注的实验室结果，INSPIRE在预处理期间移除了这些结果。

为了验证INSPIRE的质量和实用性，我们进行了一项研究，使用INSPIRE的数据开发基于机器学习的手术后30天死亡率预测模型。我们遵循了我们之前关于术后死亡率术前预测研究的方法。从'operations'表中，使用了基线特征，如年龄、性别、身高、体重和美国麻醉医师协会身体状态分级、外科科室、麻醉类型和手术急诊性。我们还从'labs'表中提取了术前实验室结果，包括血细胞计数、白细胞计数、血红蛋白水平、血小板计数、凝血酶原时间、活化部分凝血活酶时间、血清钠、钾、血尿素氮、肌酐、白蛋白、谷草转氨酶和谷丙转氨酶。从提取的变量中，我们使用逻辑回归和梯度提升开发了两个预测模型，并将预测性能与ASA-PS分级进行比较。研究结果见图2。我们的模型显示了与之前模型相似的性能，梯度提升方法的最佳性能AUROC为0.944。

结果表明，INSPIRE采用的匿名化过程（对年龄和测量值进行分类）对数据集的实用性和基于此类数据构建的预测模型性能影响最小。因此，这表明INSPIRE的匿名化过程作为一个程序，在保持数据集实用性的同时减轻重新识别风险。

## 使用说明

### 数据访问
INSPIRE在PhysioNet上公开可用。INSPIRE以逗号分隔值(CSV)文件的复合形式提供。在所有病例中，公共数据集包含总数据集的50%（约130,000例），其中subject_id以0到4结尾。寻求访问数据集的研究人员必须通过PhysioNet完成数据使用协议(DUA)，该协议声明数据集仅用于研究目的，不会向第三方披露或提供，不会尝试重新识别；数据集的提供可以随时终止。虽然我们通过匿名化过程最小化了重新识别的风险，但所有医疗数据都存在固有的重新识别风险。为了平衡这种不可避免的风险与利用数据的好处，SNUH的IRB和DRB已批准仅在DUA下使用这些数据。

### 代码可用性
使用INSPIRE开发的死亡率预测模型的示例代码在GitHub上可用，以演示数据集的简单用例。随着时间的推移，我们将与社区合作开发和共享额外的开源代码，以支持INSPIRE数据集的重用。

## 表格和图表

### 表1. INSPIRE数据集特征

| 变量 | INSPIRE数据集 (N = 131,109) |
|------|---------------------------|
| 手术时年龄，年，中位数(IQR) | 60 (45–70) |
| 性别，女/男 | 73,099/58,010 |
| 身高，cm，平均值 ± 标准差 | 162.1 ± 69.8 |
| 体重，kg，平均值 ± 标准差 | 62.7 ± 12.3 |
| ASA分级，n (%) |  |
| 1 | 43,539 (33.2%) |
| 2 | 71,688 (54.7%) |
| 3 | 11,531 (8.8%) |
| 4 | 689 (0.5%) |
| 5 | 52 (0.04%) |
| 6 | 58 (0.04%) |
| 急诊，n (%) | 12,365 (9.4%) |
| 手术科室 |  |
| 普通外科 | 34,764 (26.5%) |
| 骨科 | 17,499 (13.3%) |
| 眼科 | 17,251 (13.2%) |
| 妇产科 | 12,948 (9.9%) |
| 泌尿外科 | 12,237 (9.3%) |
| 耳鼻喉科 | 11,711(8.9%) |
| 神经外科 | 10,180 (7.8%) |
| 心胸外科 | 8,757 (6.7%) |
| 整形外科 | 5,170 (3.9%) |
| 放射科 | 379 (0.3%) |
| 内科 | 89 (0.1%) |
| 麻醉科 | 68 (0.1%) |
| 儿科 | 38 (0.03%) |
| 放射肿瘤科 | 15 (0.01%) |
| 急诊医学科 | 2 (<0.01%) |
| 皮肤科 | 1 (<0.01%) |
| 麻醉类型 n (%) |  |
| 全身麻醉 | 102,904 (78.5%) |
| 椎管内麻醉 | 13,005 (9.9%) |
| 监护麻醉护理 | 15,034 (11.5%) |
| 区域神经阻滞 | 166 (0.1%) |
| 平均手术时间，分钟，平均值 ± 标准差 | 115.2 ± 105.9 |
| 平均麻醉时间，分钟，平均值 ± 标准差 | 149.6 ± 152.7 |
| ICU入院，n (%) | 14,971 (11.42%) |
| 院内死亡率，n (%) | 1,581 (1.21%) |
| 住院时间，天，中位数(IQR) | 5 (2–9) |

IQR：四分位间距，SD：标准差，ASA：美国麻醉医师协会，ICU：重症监护病房。

### 表2. 排除的诊断

| 诊断 | ICD-10-CM |
|------|-----------|
| 精神和行为障碍 | F00~F99 |
| 性传播感染 | A50~A51, A54~A57, A60, A63, B977 |
| 人类免疫缺陷病毒相关疾病 | B20~B24, Z21 |
| 终止妊娠相关诊断 | O04 |
| 虐待相关诊断 | T74 |
| 围产期特定疾病 | P00~P96 |
| 先天性畸形、变形和染色体异常 | Q00~Q99 |
| 罕见疾病 | A31, A81, D12, D55, D56, D59~61, D64, D66~71, D76, D80~84, D86, D89, E16, E20, E22~E27, E34, E55, E70~77, E79, E80, E83~85, E88, G04, G10~12, G23~25, G31, G35, G36, G40, G41, G47, G51, G56, G57, G60, G61, G70~73, G90, G93, G95, H16, H18, H31, H35, H49, H51, I27, I42, I47, I49, I67, I73, I78, I82, J39, J84, K00, K50, K74, K75, K83, L10, L12, L73, M06, M08, M30~M35, M61, M88, M89, M92~94, N04, N25 |

ICD-10-CM：国际疾病分类第10版临床修订版。

### 图1. 本研究流程图
[描述：显示患者筛选和数据集构建过程的流程图]

### 图2. 开发的预测模型死亡率预测的受试者工作特征(ROC)曲线
GBM表示我们模型的ROC曲线。ASA：美国麻醉医师协会身体状态分级；LR：逻辑回归；GBM：梯度提升机。

## 参考文献

1. Tevis, S. E., Cobian, A. G., Truong, H. P., Craven, M. W. & Kennedy, G. D. 多重并发症对普通外科患者术后恢复的影响。Ann Surg 263, 1213–1218, https://doi.org/10.1097/SLA.0000000000001390 (2016).

2. Fink, A. S. et al. 非退伍军人管理医院的国家外科质量改进计划：可行性的初步证明。Ann Surg 236, 344–353, https://doi.org/10.1097/00000658-200209000-00011 (2002).

3. Liau, A., Havidich, J. E., Onega, T. & Dutton, R. P. 国家麻醉临床结果注册数据库。Anesth Analg 121, 1604–1610, https://doi.org/10.1213/ANE.0000000000000895 (2015).

4. Lee, H. C. et al. VitalDB，外科患者高保真多参数生命体征数据库。Sci Data 9, 279, https://doi.org/10.1038/s41597-022-01411-5 (2022).

5. Vistisen, S. T., Pollard, T. J., Enevoldsen, J. & Scheeren, T. W. L. VitalDB：促进麻醉研究合作。Br J Anaesth 127, 184–187, https://doi.org/10.1016/j.bja.2021.03.011 (2021).

6. Samad, M. et al. 医学信息学手术室生命体征和事件存储库(MOVER)：公共访问手术室数据库。JAMIA Open 6, ooad084, https://doi.org/10.1093/jamiaopen/ooad084 (2023).

7. Johnson, A. E. et al. MIMIC-III，一个免费访问的重症监护数据库。Sci Data 3, 160035, https://doi.org/10.1038/sdata.2016.35 (2016).

8. Johnson, A., Pollard, T. & Mark, R. MIMIC-III临床数据库。PhysioNet, https://doi.org/10.13026/C2XW26. (2016).

9. Bektas, M., Tuynman, J. B., Costa Pereira, J., Burchell, G. L. & van der Peet, D. L. 预测结直肠手术后手术结果的机器学习算法：系统综述。World J Surg 46, 3100–3110, https://doi.org/10.1007/s00268-022-06728-1 (2022).

10. Penny-Dimri, J. C. et al. 预测心脏手术后不良结果的机器学习：系统综述和荟萃分析。J Card Surg 37, 3838–3845, https://doi.org/10.1111/jocs.16842 (2022).

11. Senanayake, S. et al. 预测肾移植后移植物失败的机器学习：已发表预测模型的系统综述。Int J Med Inform 130, 103957, https://doi.org/10.1016/j.ijmedinf.2019.103957 (2019).

12. Steyerberg, E. W. & Harrell, F. E. Jr. 预测模型需要适当的内部、内部-外部和外部验证。J Clin Epidemiol 69, 245–247, https://doi.org/10.1016/j.jclinepi.2015.04.005 (2016).

13. 美国医疗保险和医疗补助服务中心和国家卫生统计中心。ICD-10-CM编码和报告官方指南FY 2022, https://www.cms.gov/files/document/fy-2022-icd-10-cm-coding-guidelines-updated-02012022.pdf (2023).

14. Moon, T. J. 韩国医疗保健系统的光明与阴影。J Korean Med Sci 27(Suppl), S3–6, https://doi.org/10.3346/jkms.2012.27.S.S3 (2012).

15. Tan, H., Chen, X., Chen, Y., He, B. & Wong, W.-F. 在ACM国际超级计算会议论文集115–126 (计算机协会，虚拟活动，美国，2021).

16. 韩国政府相关部委联合，2016年发布的个人信息去标识化措施指南。https://www.privacy.go.kr/cmm/fms/FileDown.do?atchFileId=FILE_000000000827059&fileSn=0 (2016).

17. 韩国立法研究院，罕见疾病管理法。https://elaw.klri.re.kr/eng_service/lawView.do?hseq=50746&lang=ENG (2019).

18. 罕见疾病信息。韩国疾病控制预防署，https://helpline.kdca.go.kr/cdchelp/ph/rdiz/selectRdizInfList.do?menu=A0100 (2023).

19. 美国医疗保险和医疗补助服务中心和国家卫生统计中心。ICD-10-PCS编码和报告官方指南。https://www.cms.gov/files/document/2022-official-icd-10-pcs-coding-guidelines-updated-december-1-2021.pdf (2023).

20. Li, N. H., Li, T. C. & Venkatasubramanian, S. t-接近性：超越k-匿名性和l-多样性的隐私。2007 IEEE第23届国际数据工程会议，106-115, https://doi.org/10.1109/ICDE.2007.367856 (2007).

21. 风险分析。ARX数据匿名化工具，https://arx.deidentifier.org/ (2023).

22. Lee, H. & Lim, L. INSPIRE，围手术期医学公开研究数据集。PhysioNet https://doi.org/10.13026/4evs-wq50 (2023).

23. Lee, S. W. et al. 术后死亡率术前预测机器学习模型的多中心验证。NPJ Digit Med 5, 91, https://doi.org/10.1038/s41746-022-00625-6 (2022).

24. Lee, H. 使用INSPIRE数据的手术后30天死亡率基于机器学习的预测模型。Github https://github.com/vitaldb/inspire/blob/main/gbm_mortality.py (2023).

25. Lee, H. INSPIRE数据集利用支持平台。https://github.com/vitaldb/inspire (2023).

## 致谢

Leo A Celi由美国国立卫生研究院通过R01 EB017205、DS-I Africa U54 TW012043-01和Bridge2AI OT2OD032701资助，以及美国国家科学基金会通过ITEST #2148451资助。Hyung-Chul Lee由韩国保健产业振兴院(KHIDI)资助的韩国卫生技术研发项目支持，该项目由韩国保健福祉部资助（资助号：HI21C107409）。

## 作者贡献

Leerang Lim：工作的构思和设计，数据分析和解释，起草手稿。
Hyeonhoon Lee：数据分析和解释，创建工作中使用的新软件。
Chul-Woo Jung：工作的构思和设计，修订草稿。
Dayeon Sim：数据获取。
Xavier Borrat：工作设计，数据解释，修订草稿。
Tom J. Pollard：工作设计，数据解释，修订草稿。
Leo A. Celi：工作设计，数据解释。
Roger G. Mark：工作设计，数据解释。
Simon T. Vistisen：工作构思，修订草稿。
Hyung-Chul Lee：工作的构思和设计，起草和修订手稿。

## 竞争利益

作者声明无竞争利益。

## 附加信息

补充信息 在线版本包含补充材料，可在https://doi.org/10.1038/s41597-024-03517-4获得。

通讯和材料请求应发送至H.-C.L.
重印和许可信息可在www.nature.com/reprints获得。

出版商说明 Springer Nature对已发表地图和机构隶属关系中的管辖权声明保持中立。

开放获取 本文根据知识共享署名4.0国际许可证获得许可，该许可证允许在任何媒体或格式中使用、共享、改编、分发和复制，只要您适当地归功于原作者和来源，提供知识共享许可证的链接，并说明是否进行了更改。本文中的图像或其他第三方材料包含在文章的知识共享许可证中，除非在材料的信用行中另有说明。如果材料不包含在文章的知识共享许可证中，并且您的预期用途不被法定法规允许或超出允许用途，您需要直接从版权持有者获得许可。要查看此许可证的副本，请访问http://creativecommons.org/licenses/by/4.0/。© 作者 2024
